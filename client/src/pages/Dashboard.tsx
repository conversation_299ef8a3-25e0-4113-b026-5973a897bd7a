import { useContext, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ProgressCard from "@/components/ProgressCard";
import TopicCard from "@/components/TopicCard";
import AchievementBadge from "@/components/AchievementBadge";
import TopNavigation from "@/components/TopNavigation";
import { Button } from "@/components/ui/button";
import { UserContext } from "../App";
import { Topic, Chapter } from "@shared/schema";
import { signOut } from "@/lib/supabase";
import { Icons } from "@/components/ui/icons";

// Topic-specific colors for consistent UI
const TOPIC_COLORS = ["primary", "secondary", "accent", "blue-500", "purple-500", "green-500", "rose-500", "amber-500"];

export default function Dashboard() {
  const { user } = useContext(UserContext);
  const [, navigate] = useLocation();
  const [isSigningOut, setIsSigningOut] = useState(false);
  
  // <PERSON>le sign out
  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsSigningOut(false);
    }
  };
  
  // Fetch topics
  const { data: topics = [], isLoading: isLoadingTopics } = useQuery({
    queryKey: ["/api/topics"],
    enabled: !!user,
  });
  
  // State to track chapters for all topics
  const [topicChapters, setTopicChapters] = useState<Record<number, any[]>>({});
  const [allChapters, setAllChapters] = useState<any[]>([]);
  
  // Fetch chapters for each topic
  useEffect(() => {
    if (!Array.isArray(topics) || topics.length === 0 || !user) return;
    
    const fetchChaptersForTopics = async () => {
      const chaptersData: Record<number, any[]> = {};
      const allChaptersList: any[] = [];
      
      for (const topic of topics) {
        try {
          const response = await fetch(`/api/topics/${topic.id}/chapters`);
          if (response.ok) {
            const chapters = await response.json();
            chaptersData[topic.id] = chapters;
            allChaptersList.push(...chapters);
          }
        } catch (error) {
          console.error(`Error fetching chapters for topic ${topic.id}:`, error);
          chaptersData[topic.id] = [];
        }
      }
      
      setTopicChapters(chaptersData);
      setAllChapters(allChaptersList);
    };
    
    fetchChaptersForTopics();
  }, [topics, user]);
  
  // Fetch user progress
  const { data: userProgress = [] } = useQuery({
    queryKey: [`/api/users/${user?.id}/progress`],
    enabled: !!user,
  });
  
  // Fetch user achievements
  const { data: achievements = [] } = useQuery({
    queryKey: ["/api/achievements"],
    enabled: !!user,
  });
  
  const { data: userAchievements = [] } = useQuery({
    queryKey: [`/api/users/${user?.id}/achievements`],
    enabled: !!user,
  });

  // Calculate progress percentages
  const calculateProgress = (chapters: any[], userProgress: any[]) => {
    if (!chapters?.length) return { percentage: 0, completed: 0, total: 0 };
    
    const completedChapters = userProgress.filter(
      (progress: any) => progress.completed && chapters.some((chapter: any) => chapter.id === progress.chapterId)
    ).length;
    
    const percentage = Math.round((completedChapters / chapters.length) * 100);
    
    return {
      percentage,
      completed: completedChapters,
      total: chapters.length
    };
  };
  
  // Calculate progress for each topic
  const topicProgress: Record<number, {percentage: number, completed: number, total: number}> = {};
  
  // Calculate overall totals
  let totalCompleted = 0;
  let totalChapters = 0;
  
  // Process each topic's progress
  Object.entries(topicChapters).forEach(([topicId, chapters]) => {
    const progress = calculateProgress(chapters, userProgress);
    topicProgress[parseInt(topicId)] = progress;
    
    totalCompleted += progress.completed;
    totalChapters += progress.total;
  });
  
  // Calculate overall progress
  const overallProgress = {
    percentage: totalChapters > 0 ? Math.round((totalCompleted / totalChapters) * 100) : 0,
    completed: totalCompleted,
    total: totalChapters
  };

  // Determine which achievements are unlocked
  const unlockedAchievements = Array.isArray(userAchievements) 
    ? userAchievements.map((ua: any) => ua.achievement?.id)
    : [];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      {/* Chapter Navigation */}
      {Array.isArray(topics) && topics.length > 0 && (
        <TopNavigation 
          topics={topics as Topic[]} 
          chapters={allChapters as Chapter[]}
          currentTopicId={topics[0]?.id}
        />
      )}
      
      <main className="flex-grow container mx-auto px-4 py-6">
        {/* Welcome Section */}
        <section className="mb-8">
          <div className="flex items-start md:items-center flex-col md:flex-row">
            <div className="flex-grow">
              <h2 className="font-baloo font-bold text-3xl mb-2">
                Hello, <span className="text-primary">{user?.firstName || user?.email?.split('@')[0] || "Friend"}</span>! 👋
              </h2>
              <p className="text-text-light text-lg">Ready to learn some math today?</p>
            </div>
            <div className="mt-4 md:mt-0 flex flex-col items-end gap-3">
              <div className="flex items-center gap-4">
                {user?.profileImageUrl && (
                  <img 
                    src={user.profileImageUrl} 
                    alt="Profile"
                    className="w-10 h-10 rounded-full border-2 border-primary object-cover shadow-md" 
                  />
                )}
                <Button 
                  variant="destructive"
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  className="py-2 px-4 rounded-lg flex items-center gap-2"
                >
                  {isSigningOut ? (
                    <>
                      <Icons.spinner className="h-5 w-5" />
                      <span>Signing out...</span>
                    </>
                  ) : (
                    <>
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        className="h-5 w-5"
                      >
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                        <polyline points="16 17 21 12 16 7" />
                        <line x1="21" y1="12" x2="9" y2="12" />
                      </svg>
                      <span>Sign Out</span>
                    </>
                  )}
                </Button>
              </div>
              <div className="animate-bounce">
                <svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="48" cy="48" r="48" fill="#FFD93D" />
                  <circle cx="34" cy="36" r="6" fill="#333333" />
                  <circle cx="62" cy="36" r="6" fill="#333333" />
                  <path d="M32 58C40 70 56 70 64 58" stroke="#333333" strokeWidth="4" strokeLinecap="round" />
                  <path d="M24 24C24 24 28 14 48 14C68 14 72 24 72 24" stroke="#333333" strokeWidth="4" strokeLinecap="round" />
                </svg>
              </div>
            </div>
          </div>
        </section>
        
        {/* Progress Section */}
        <section className="bg-white rounded-2xl p-6 shadow-sm mb-8">
          <h3 className="font-baloo font-semibold text-xl mb-4">Your Progress</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Show progress cards for each topic */}
            {Array.isArray(topics) && topics.slice(0, 2).map((topic: any, index: number) => (
              <ProgressCard
                key={topic.id}
                title={topic.name}
                percentage={topicProgress[topic.id]?.percentage || 0}
                completedCount={topicProgress[topic.id]?.completed || 0}
                totalCount={topicProgress[topic.id]?.total || 0}
                color={index % 2 === 0 ? "primary" : "secondary"}
              />
            ))}
            
            {/* Overall progress */}
            <ProgressCard
              title="Overall"
              percentage={overallProgress.percentage}
              completedCount={overallProgress.completed}
              totalCount={overallProgress.total}
              color="accent"
            />
          </div>
        </section>
        
        {/* Achievements Section */}
        <section className="bg-white rounded-2xl p-6 shadow-sm mb-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-baloo font-semibold text-xl">Your Achievements</h3>
            <a href="#" className="text-primary text-sm font-medium">View All</a>
          </div>
          <div className="flex flex-wrap gap-4">
            {Array.isArray(achievements) && achievements.map((achievement: any) => (
              <AchievementBadge
                key={achievement.id}
                title={achievement.title}
                icon={achievement.icon}
                color={achievement.color}
                unlocked={unlockedAchievements.includes(achievement.id)}
              />
            ))}
          </div>
        </section>
        
        {/* Topics Section */}
        <section>
          <div className="flex justify-between items-center mb-6">
            <h3 className="font-baloo font-semibold text-2xl">Continue Learning</h3>
            
            <Button
              onClick={() => window.location.href = "/chapter/select"}
              variant="outline"
              className="flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
              </svg>
              Browse All Topics
            </Button>
          </div>
          
          {isLoadingTopics ? (
            <div className="flex justify-center p-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : !Array.isArray(topics) || topics.length === 0 ? (
            <div className="text-center p-12 bg-white rounded-xl shadow-sm">
              <h3 className="text-xl font-semibold mb-2">No topics available yet</h3>
              <p className="text-gray-600 mb-4">The administrator is working on adding educational content.</p>
              <Button onClick={() => window.location.reload()}>Refresh</Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {topics.map((topic: any, index: number) => {
                const chaptersList = topicChapters[topic.id] || [];
                if (!chaptersList || chaptersList.length === 0) return null;
                
                const currentChapter = chaptersList[0]; // First chapter by default
                
                // Find the first incomplete chapter or use the first chapter
                const progressItems = Array.isArray(userProgress) 
                  ? userProgress.filter((p: any) => 
                      chaptersList.some((c: any) => c.id === p.chapterId)
                    )
                  : [];
                
                const incompleteCh = chaptersList.find((ch: any) => 
                  !progressItems.some((p: any) => p.chapterId === ch.id && p.completed)
                );
                
                const chapterToShow = incompleteCh || currentChapter;
                
                // Calculate colorIndex for topics with different colors
                const colorIndex = index % TOPIC_COLORS.length;
                
                return (
                  <TopicCard
                    key={topic.id}
                    id={chapterToShow.id}
                    title={topic.name}
                    imageUrl={topic.imageUrl || `https://cdn.pixabay.com/photo/2023/07/11/09/59/ai-generated-8120355_1280.jpg`}
                    chapterTitle={chapterToShow.title}
                    chapterDescription={chapterToShow.description}
                    currentChapter={chaptersList.findIndex((ch: any) => ch.id === chapterToShow.id) + 1}
                    totalChapters={chaptersList.length}
                    color={TOPIC_COLORS[colorIndex]}
                  />
                );
              })}
            </div>
          )}
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
