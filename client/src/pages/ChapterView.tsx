import { useContext, useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ExerciseCard from "@/components/ExerciseCard";
import TopicSelection from "@/components/TopicSelection";
import ChapterIntro from "@/components/ChapterIntro";
import { UserContext } from "../App";
import { updateProgress } from "@/lib/openai";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Topic, Chapter } from "@shared/schema";
import TopNavigation from "@/components/TopNavigation";

export default function ChapterView() {
  const { id } = useParams();
  const chapterId = parseInt(id);
  const { user } = useContext(UserContext);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [chapterCompleted, setChapterCompleted] = useState(false);
  const [totalScore, setTotalScore] = useState(0);
  const [showIntro, setShowIntro] = useState(true);
  const [showTopicSelection, setShowTopicSelection] = useState(!id || id === "select");

  // Fetch all topics
  const { data: topics = [], isLoading: isLoadingTopics } = useQuery({
    queryKey: ["/api/topics"],
    enabled: !!user,
  });

  // Fetch chapter data
  const { data: chapter, isLoading: isLoadingChapter } = useQuery({
    queryKey: [`/api/chapters/${chapterId}`],
    enabled: !!chapterId && !!user && !showTopicSelection,
  });

  // Fetch topic data
  const { data: topic, isLoading: isLoadingTopic } = useQuery({
    queryKey: [`/api/topics/${chapter?.topicId}`],
    enabled: !!chapter?.topicId,
  });

  // Fetch all chapters for topic selection
  const { data: allChapters = [], isLoading: isLoadingAllChapters } = useQuery({
    queryKey: [`/api/chapters`],
    enabled: !!user && showTopicSelection,
  });

  // Fetch exercises for this chapter
  const { data: exercises = [], isLoading: isLoadingExercises } = useQuery({
    queryKey: [`/api/chapters/${chapterId}/exercises`],
    enabled: !!chapterId && !!user && !showTopicSelection,
  });

  // Fetch user progress for this chapter
  const { data: chapterProgress, isLoading: isLoadingProgress } = useQuery({
    queryKey: [`/api/users/${user?.id}/progress/${chapterId}`],
    enabled: !!user?.id && !!chapterId && !showTopicSelection,
  });
  
  // Organize chapters by topic for selection
  const topicChapters = (allChapters as Chapter[]).reduce((acc, chapter) => {
    if (!acc[chapter.topicId]) {
      acc[chapter.topicId] = [];
    }
    acc[chapter.topicId].push(chapter);
    return acc;
  }, {} as Record<number, Chapter[]>);

  // Mutation for updating progress
  const updateProgressMutation = useMutation({
    mutationFn: (data: { userId: number; chapterId: number; completed: boolean; score?: number }) =>
      updateProgress(data.userId, data.chapterId, data.completed, data.score),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/users/${user?.id}/progress`] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${user?.id}/progress/${chapterId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${user?.id}/achievements`] });
    },
  });

  // Handle exercise completion
  const handleExerciseComplete = (score: number) => {
    setTotalScore(prev => prev + score);
    
    // Move to next exercise or complete chapter
    if (currentExerciseIndex < (exercises as any[]).length - 1) {
      setCurrentExerciseIndex(prev => prev + 1);
    } else {
      // Chapter completed
      setChapterCompleted(true);
      
      // Update progress in database
      if (user) {
        updateProgressMutation.mutate({
          userId: parseInt(user.id), // Ensure it's a number
          chapterId,
          completed: true,
          score: totalScore + score
        });
        
        toast({
          title: "Chapter Completed!",
          description: `Congratulations! You've earned ${totalScore + score} points.`,
          variant: "default",
        });
      }
    }
  };

  // Handle going back to dashboard
  const navigateBack = () => {
    setLocation("/");
  };
  
  // Start the learning experience
  const startLearning = () => {
    setShowIntro(false);
  };
  
  // Handle selecting a different chapter
  const selectChapter = (selectedChapterId: number) => {
    setLocation(`/chapter/${selectedChapterId}`);
  };

  // Determine if we're loading based on what we're showing
  const isLoading = showTopicSelection 
    ? isLoadingTopics || isLoadingAllChapters
    : isLoadingChapter || isLoadingTopic || isLoadingExercises || isLoadingProgress;

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow container mx-auto px-4 py-6 flex items-center justify-center">
          <div className="text-center">
            <svg
              className="animate-spin h-12 w-12 text-primary mx-auto mb-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <p className="text-lg font-medium">Loading chapter content...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Show topic selection if no chapter is selected or if we're in selection mode
  if (showTopicSelection) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <TopNavigation 
          topics={topics as Topic[]} 
          chapters={allChapters as Chapter[]} 
        />
        <main className="flex-grow container mx-auto px-4 py-6">
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <button
                className="mr-4 bg-primary bg-opacity-10 rounded-full p-2 text-primary hover:bg-opacity-20"
                onClick={navigateBack}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="m15 18-6-6 6-6" />
                </svg>
              </button>
              <h2 className="font-baloo font-bold text-3xl">Choose a Topic</h2>
            </div>
            <p className="text-gray-600">
              Select a topic and chapter to begin your learning journey
            </p>
          </div>
          
          <TopicSelection 
            topics={topics as Topic[]} 
            topicChapters={topicChapters}
          />
        </main>
        <Footer />
      </div>
    );
  }
  
  // If chapter exists but no exercises found
  if (!chapter || (exercises as any[]).length === 0) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow container mx-auto px-4 py-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Chapter not found</h2>
            <p className="mb-4 text-gray-600">
              Sorry, we couldn't find any content for this chapter. Please try another one.
            </p>
            <Button onClick={() => setShowTopicSelection(true)}>Browse Topics</Button>
            <Button onClick={navigateBack} variant="outline" className="ml-2">Return to Dashboard</Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const currentExercise = exercises[currentExerciseIndex];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <TopNavigation 
        topics={topics as Topic[]} 
        chapters={allChapters as Chapter[]} 
        currentTopicId={(chapter as Chapter)?.topicId}
      />
      
      <main className="flex-grow container mx-auto px-4 py-6">
        {/* Chapter Header */}
        <div className="flex items-center mb-6">
          <button
            className="mr-4 bg-primary bg-opacity-10 rounded-full p-2 text-primary hover:bg-opacity-20"
            onClick={navigateBack}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
          </button>
          <div>
            <h2 className="font-baloo font-bold text-3xl">{(chapter as Chapter)?.title}</h2>
            <p className="text-text-light">{(topic as Topic)?.name} - Chapter {(chapter as Chapter)?.order}</p>
          </div>
        </div>
        
        {/* Topic Selection Button */}
        <div className="mb-6">
          <Button 
            variant="outline" 
            onClick={() => setShowTopicSelection(true)}
            className="mb-4"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="3" x2="9" y2="21"></line>
            </svg>
            Browse All Topics
          </Button>
        </div>
        
        {/* Chapter Introduction (only show if in intro mode) */}
        {showIntro && !chapterCompleted && (
          <ChapterIntro 
            chapter={chapter as Chapter}
            topic={topic as Topic}
            onStartLearning={startLearning}
          />
        )}
        
        {/* Exercises - Only show when not in intro mode */}
        {!showIntro && !chapterCompleted ? (
          <div className="bg-white rounded-2xl p-6 shadow-sm mb-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-baloo font-semibold text-xl">Exercise {currentExerciseIndex + 1} of {(exercises as any[]).length}</h3>
              <Button 
                variant="ghost"
                onClick={() => setShowIntro(true)}
                className="text-primary"
                size="sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="12"></line>
                  <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
                Chapter Info
              </Button>
            </div>
            
            <div className="bg-gray-50 rounded-xl p-1 mb-6">
              <div 
                className="bg-primary rounded-lg h-2"
                style={{ width: `${((currentExerciseIndex + 1) / (exercises as any[]).length) * 100}%` }}
              ></div>
            </div>
            
            {(exercises as any[])[currentExerciseIndex] && (
              <ExerciseCard
                exerciseNumber={currentExerciseIndex + 1}
                totalExercises={(exercises as any[]).length}
                title={(exercises as any[])[currentExerciseIndex].title}
                description={(exercises as any[])[currentExerciseIndex].description}
                difficulty={(exercises as any[])[currentExerciseIndex].difficulty}
                content={(exercises as any[])[currentExerciseIndex].content}
                type={(exercises as any[])[currentExerciseIndex].type}
                exerciseId={(exercises as any[])[currentExerciseIndex].id}
                onComplete={handleExerciseComplete}
              />
            )}
          </div>
        ) : chapterCompleted ? (
          <section className="bg-success bg-opacity-10 rounded-2xl p-8 shadow-sm mb-8 text-center">
            <div className="mb-6">
              <div className="bg-success rounded-full p-4 mx-auto w-20 h-20 flex items-center justify-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-10 w-10 text-white"
                >
                  <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z" />
                </svg>
              </div>
              <h3 className="font-baloo font-semibold text-2xl text-success mb-2">
                Chapter Completed!
              </h3>
              <p className="text-lg mb-4">
                Amazing job! You've completed all exercises in this chapter.
              </p>
              <p className="font-bold text-xl mb-6">
                Total Score: {totalScore} points
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row justify-center gap-3">
              <Button
                onClick={() => setShowTopicSelection(true)}
                className="bg-primary text-white font-semibold py-3 px-8 rounded-xl hover:bg-opacity-90 transition-colors text-lg"
              >
                Continue Learning
              </Button>
              
              <Button
                onClick={navigateBack}
                variant="outline"
                className="font-semibold py-3 px-8 rounded-xl text-lg"
              >
                Return to Dashboard
              </Button>
            </div>
          </section>
        ) : null}
      </main>
      
      <Footer />
    </div>
  );
}
