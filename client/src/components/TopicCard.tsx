import { Link } from "wouter";

interface TopicCardProps {
  id: number;
  title: string;
  imageUrl: string;
  chapterTitle: string;
  chapterDescription: string;
  currentChapter: number;
  totalChapters: number;
  color: string;
}

export default function TopicCard({
  id,
  title,
  imageUrl,
  chapterTitle,
  chapterDescription,
  currentChapter,
  totalChapters,
  color,
}: TopicCardProps) {
  const bgColor = `bg-${color}`;
  const hoverBgOpacity = `hover:bg-opacity-90`;
  
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
      <div className="relative">
        <img 
          src={imageUrl} 
          alt={`${title} Topic`} 
          className="w-full h-48 object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end p-6">
          <h4 className="font-baloo font-bold text-2xl text-white">{title}</h4>
        </div>
      </div>
      <div className="p-6">
        <div className="mb-4">
          <h5 className="font-semibold mb-2">Current Chapter: {chapterTitle}</h5>
          <p className="text-text-light text-sm">{chapterDescription}</p>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-text-light">
            Chapter {currentChapter} of {totalChapters}
          </span>
          <Link href={`/chapter/${id}`} className={`${bgColor} text-white font-semibold py-2 px-6 rounded-xl ${hoverBgOpacity} transition-colors`}>
            Continue
          </Link>
        </div>
      </div>
    </div>
  );
}
