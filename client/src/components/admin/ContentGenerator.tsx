import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";

// Define the topic categories
const TOPIC_CATEGORIES = [
  "Arithmetic",
  "Algebra",
  "Geometry",
  "Measurement",
  "Statistics"
];

// Topic types for the form
interface TopicFormData {
  name: string;
  category: string;
  description: string;
  lessonCount: number;
  exercisesPerLesson: number;
  ageGroup: string;
  instructions: string;
}

export default function ContentGenerator() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<any>(null);
  const [showGenerated, setShowGenerated] = useState(false);
  const [existingTopics, setExistingTopics] = useState<any[]>([]);
  const [isLoadingTopics, setIsLoadingTopics] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState<TopicFormData>({
    name: "",
    category: "Arithmetic",
    description: "",
    lessonCount: 3,
    exercisesPerLesson: 2,
    ageGroup: "5-8",
    instructions: ""
  });
  
  const { toast } = useToast();
  
  // Load existing topics when component mounts
  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setIsLoadingTopics(true);
        const response = await fetch('/api/topics');
        if (response.ok) {
          const topics = await response.json();
          setExistingTopics(topics);
        }
      } catch (error) {
        console.error("Failed to fetch topics", error);
      } finally {
        setIsLoadingTopics(false);
      }
    };
    
    fetchTopics();
  }, []);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle number inputs
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numberValue = parseInt(value);
    if (!isNaN(numberValue) && numberValue > 0) {
      setFormData(prev => ({
        ...prev,
        [name]: numberValue
      }));
    }
  };
  
  const generateContent = async () => {
    if (isGenerating) return;
    
    try {
      setIsGenerating(true);
      toast({
        title: "Starting content generation",
        description: `Generating content for ${formData.name}...`,
      });
      
      // Create a simulated topic and chapters to show as a preview
      const simulatedContent = {
        topic: {
          id: Math.floor(Math.random() * 1000) + 10,
          name: formData.name,
          description: formData.description || `Learn about ${formData.name} in a fun and interactive way.`,
          category: formData.category,
          imageUrl: `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=${encodeURIComponent(formData.name)}`
        },
        chapters: Array.from({ length: formData.lessonCount }, (_, i) => {
          const title = i === 0 ? `Introduction to ${formData.name}` : `${formData.name} Lesson ${i + 1}`;
          return {
            id: Math.floor(Math.random() * 1000) + 100,
            topicId: 0,
            title,
            description: `Learn about ${formData.name} in an age-appropriate way for ${formData.ageGroup} year olds.`,
            order: i + 1,
            durationMinutes: 15 + Math.floor(Math.random() * 10),
            exerciseCount: formData.exercisesPerLesson,
            pointsReward: (i + 1) * 10,
            exercises: Array.from({ length: formData.exercisesPerLesson }, (_, j) => {
              const types = ["multiple-choice", "fill-in-blank", "matching"];
              const type = types[j % types.length];
              const difficulty = ["easy", "medium", "hard"][Math.floor(Math.random() * 3)];
              
              let content;
              // Create exercises with clear correct answers for evaluation
              if (type === "multiple-choice") {
                // For shapes topic
                if (formData.name.toLowerCase().includes("shape")) {
                  content = {
                    question: `Which of these is a ${["triangle", "square", "circle", "rectangle"][j % 4]}?`,
                    options: ["A square has 4 equal sides", "A triangle has 3 sides", "A circle is perfectly round", "A rectangle has 4 sides with opposite sides equal"],
                    correctAnswer: ["A square has 4 equal sides", "A triangle has 3 sides", "A circle is perfectly round", "A rectangle has 4 sides with opposite sides equal"][j % 4]
                  };
                } 
                // For numbers/arithmetic topics
                else if (formData.category === "Arithmetic") {
                  const num1 = Math.floor(Math.random() * 10) + 1;
                  const num2 = Math.floor(Math.random() * 10) + 1;
                  const answer = num1 + num2;
                  content = {
                    question: `What is ${num1} + ${num2}?`,
                    options: [answer - 1, answer, answer + 1, answer + 2].map(String),
                    correctAnswer: String(answer)
                  };
                } 
                // Default for other topics
                else {
                  content = {
                    question: `Sample question about ${formData.name} (${j + 1})`,
                    options: ["Option A", "Option B", "Option C", "Option D"],
                    correctAnswer: "Option B"
                  };
                }
              } else if (type === "fill-in-blank") {
                if (formData.category === "Arithmetic") {
                  const num1 = Math.floor(Math.random() * 10) + 1;
                  const num2 = Math.floor(Math.random() * 10) + 1;
                  const answer = num1 + num2;
                  content = {
                    question: `Fill in the blank: ${num1} + ${num2} = ______`,
                    answer: String(answer)
                  };
                } else {
                  content = {
                    question: `Fill in the blank: ______ is a key concept in ${formData.name}.`,
                    answer: formData.name
                  };
                }
              } else {
                // Matching exercise
                if (formData.category === "Geometry") {
                  content = {
                    question: `Match the shapes with their properties`,
                    pairs: [
                      { term: "Triangle", definition: "Has 3 sides" },
                      { term: "Square", definition: "Has 4 equal sides" },
                      { term: "Circle", definition: "Has no corners" }
                    ]
                  };
                } else {
                  content = {
                    question: `Match the following terms related to ${formData.name}`,
                    pairs: [
                      { term: `${formData.name} Term 1`, definition: "Definition 1" },
                      { term: `${formData.name} Term 2`, definition: "Definition 2" },
                      { term: `${formData.name} Term 3`, definition: "Definition 3" }
                    ]
                  };
                }
              }
              
              return {
                id: Math.floor(Math.random() * 1000) + 200,
                chapterId: 0,
                title: `Exercise ${j + 1}`,
                description: `Practice your knowledge of ${formData.name}.`,
                difficulty,
                type,
                content,
                order: j + 1
              };
            })
          };
        })
      };
      
      // Save topic to database
      const topicResponse = await fetch('/api/topics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          category: formData.category,
          description: simulatedContent.topic.description,
          imageUrl: simulatedContent.topic.imageUrl
        })
      });
      
      if (!topicResponse.ok) {
        throw new Error("Failed to create topic");
      }
      
      const savedTopic = await topicResponse.json();
      simulatedContent.topic.id = savedTopic.id;
      
      // Create chapters and exercises
      for (const chapter of simulatedContent.chapters) {
        const chapterResponse = await fetch('/api/chapters', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            topicId: savedTopic.id,
            title: chapter.title,
            description: chapter.description,
            order: chapter.order,
            durationMinutes: chapter.durationMinutes,
            exerciseCount: chapter.exerciseCount,
            pointsReward: chapter.pointsReward
          })
        });
        
        if (!chapterResponse.ok) {
          throw new Error(`Failed to create chapter: ${chapter.title}`);
        }
        
        const savedChapter = await chapterResponse.json();
        chapter.id = savedChapter.id;
        
        // Create exercises for each chapter
        for (const exercise of chapter.exercises) {
          const exerciseResponse = await fetch('/api/exercises', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              chapterId: savedChapter.id,
              title: exercise.title,
              description: exercise.description,
              difficulty: exercise.difficulty,
              type: exercise.type,
              content: exercise.content,
              order: exercise.order
            })
          });
          
          if (!exerciseResponse.ok) {
            throw new Error(`Failed to create exercise: ${exercise.title}`);
          }
        }
      }
      
      // Set the generated content in state to display it
      setGeneratedContent(simulatedContent);
      setShowGenerated(true);
      
      toast({
        title: "Content generation complete",
        description: `Content for ${formData.name} has been created and saved to the database!`,
        variant: "default",
      });
      
      // Refresh the topics list
      const topicsResponse = await fetch('/api/topics');
      if (topicsResponse.ok) {
        const topics = await topicsResponse.json();
        setExistingTopics(topics);
      }
      
    } catch (error: any) {
      toast({
        title: "Generation failed",
        description: error.message || "There was an error generating the content.",
        variant: "destructive",
      });
      console.error("Error generating content:", error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // No need for separate saveToDatabase function anymore since 
  // our API endpoint handles database saving automatically

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Math Content Generator</h2>
      <p className="mb-4 text-gray-700">
        Generate educational content for math topics including chapters, lessons, and exercises.
        Customize the content generation using the form below.
      </p>
      
      <div className="space-y-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Topic Name</Label>
            <Input 
              id="name" 
              name="name" 
              value={formData.name} 
              onChange={handleInputChange}
              placeholder="e.g., Addition, Fractions, Shapes"
            />
          </div>
          
          <div>
            <Label htmlFor="category">Category</Label>
            <Select 
              value={formData.category} 
              onValueChange={(value) => handleSelectChange('category', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {TOPIC_CATEGORIES.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea 
            id="description" 
            name="description" 
            value={formData.description} 
            onChange={handleInputChange}
            placeholder="Describe what students will learn in this topic"
            rows={2}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="lessonCount">Number of Lessons</Label>
            <Input 
              id="lessonCount" 
              name="lessonCount" 
              type="number" 
              min="1" 
              max="10"
              value={formData.lessonCount} 
              onChange={handleNumberChange}
            />
          </div>
          
          <div>
            <Label htmlFor="exercisesPerLesson">Exercises Per Lesson</Label>
            <Input 
              id="exercisesPerLesson" 
              name="exercisesPerLesson" 
              type="number" 
              min="1" 
              max="5"
              value={formData.exercisesPerLesson} 
              onChange={handleNumberChange}
            />
          </div>
          
          <div>
            <Label htmlFor="ageGroup">Age Group</Label>
            <Select 
              value={formData.ageGroup} 
              onValueChange={(value) => handleSelectChange('ageGroup', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select age group" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5-8">5-8 years</SelectItem>
                <SelectItem value="9-12">9-12 years</SelectItem>
                <SelectItem value="13-15">13-15 years</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div>
          <Label htmlFor="instructions">Special Instructions (Optional)</Label>
          <Textarea 
            id="instructions" 
            name="instructions" 
            value={formData.instructions} 
            onChange={handleInputChange}
            placeholder="Add any specific instructions for the AI to follow when generating content"
            rows={3}
          />
        </div>
      </div>
      
      <div className="flex flex-wrap gap-4 mb-6">
        <Button 
          onClick={generateContent} 
          disabled={isGenerating || !formData.name}
          className="relative"
        >
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating Content with AI...
            </>
          ) : (
            "Generate Content"
          )}
        </Button>
        
        <div className="text-sm text-gray-500 flex items-center">
          {isGenerating ? 
            "Creating educational content with AI. This may take a minute..." : 
            "Content will be automatically generated and saved to the database"}
        </div>
      </div>
      
      {/* Display existing topics */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Existing Topics</h3>
        {isLoadingTopics ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
            <span>Loading topics...</span>
          </div>
        ) : existingTopics.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {existingTopics.map(topic => (
              <div key={topic.id} className="border rounded p-3 bg-gray-50">
                <h4 className="font-medium">{topic.name}</h4>
                <p className="text-xs text-gray-500">{topic.category}</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500">No topics available yet. Generate and save some content!</p>
        )}
      </div>
      
      {/* Display generated content */}
      {showGenerated && generatedContent && (
        <div className="mt-6 border rounded-lg p-4 bg-gray-50">
          <h3 className="text-lg font-semibold mb-2">Generated Content Preview:</h3>
          <div className="mb-4">
            <h4 className="text-md font-medium">Topic: {generatedContent.topic.name}</h4>
            <p className="text-sm text-gray-600">{generatedContent.topic.description}</p>
            <div className="text-xs text-gray-500 mt-1">Category: {generatedContent.topic.category}</div>
          </div>
          
          <div className="space-y-3">
            <h4 className="text-md font-medium">Chapters/Lessons ({generatedContent.chapters.length}):</h4>
            {generatedContent.chapters.map((chapter: any, index: number) => (
              <div key={index} className="border rounded p-3 bg-white">
                <h5 className="font-medium">{chapter.title}</h5>
                <p className="text-sm text-gray-600 mb-2">{chapter.description}</p>
                <div className="text-xs text-gray-500 mb-2">
                  Duration: {chapter.durationMinutes} minutes | 
                  Exercises: {chapter.exerciseCount} | 
                  Points: {chapter.pointsReward}
                </div>
                
                {chapter.exercises && chapter.exercises.length > 0 && (
                  <div className="mt-2">
                    <p className="text-xs font-medium mb-1">Exercises:</p>
                    <div className="space-y-2">
                      {chapter.exercises.map((exercise: any, exIndex: number) => (
                        <div key={exIndex} className="pl-3 border-l-2 border-primary text-xs">
                          <p className="font-medium">{exercise.title} - {exercise.difficulty}</p>
                          <p className="text-gray-600">{exercise.description}</p>
                          <p className="text-gray-500">Type: {exercise.type}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 bg-yellow-50 p-3 rounded border border-yellow-200">
            <h4 className="text-sm font-medium text-yellow-800">Content Generation Notes:</h4>
            <p className="text-xs text-yellow-700 mt-1">
              This is a simulated preview of what would be generated with OpenAI. In a real implementation, 
              the content would be much more detailed and educational, following your specific instructions.
              Click "Save to Database" to store this content.
            </p>
          </div>
        </div>
      )}
    </Card>
  );
}