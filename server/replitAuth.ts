import * as client from "openid-client";
import { Strategy, type VerifyFunction } from "openid-client/passport";

import passport from "passport";
import session from "express-session";
import type { Express, RequestHandler } from "express";
import memoize from "memoizee";
import connectPg from "connect-pg-simple";
import { storage } from "./storage";

// We can access REPL_ID and REPL_SLUG in the environment
if (!process.env.REPL_ID) {
  console.warn("REPL_ID not found in environment, using fallback values for auth");
}

// Use current hostname if REPLIT_DOMAINS is not set
if (!process.env.REPLIT_DOMAINS) {
  // In development, use localhost
  const hostname = "localhost:5000";
  process.env.REPLIT_DOMAINS = hostname;
  console.log(`Setting REPLIT_DOMAINS to ${hostname}`);
}

// Set REPL_ID if not available (for development)
if (!process.env.REPL_ID) {
  process.env.REPL_ID = "dev-repl-id";
  console.log("Setting fallback REPL_ID for development");
}

const getOidcConfig = memoize(
  async () => {
    return await client.discovery(
      new URL(process.env.ISSUER_URL ?? "https://replit.com/oidc"),
      process.env.REPL_ID!
    );
  },
  { maxAge: 3600 * 1000 }
);

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000; // 1 week
  const pgStore = connectPg(session);
  const sessionStore = new pgStore({
    conString: process.env.DATABASE_URL,
    createTableIfMissing: true,
    ttl: sessionTtl,
    tableName: "sessions",
  });
  return session({
    secret: process.env.SESSION_SECRET ?? "dev-session-secret",
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      // Only use secure in production
      secure: process.env.NODE_ENV === 'production',
      maxAge: sessionTtl,
      sameSite: 'lax'
    },
  });
}

function updateUserSession(
  user: any,
  tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers
) {
  user.claims = tokens.claims();
  user.access_token = tokens.access_token;
  user.refresh_token = tokens.refresh_token;
  user.expires_at = user.claims?.exp;
}

async function upsertUser(
  claims: any,
) {
  try {
    console.log("Upserting user with claims:", JSON.stringify(claims, null, 2));
    await storage.upsertUser({
      id: claims["sub"],
      email: claims["email"],
      firstName: claims["first_name"],
      lastName: claims["last_name"],
      profileImageUrl: claims["profile_image_url"],
    });
    console.log("User upserted successfully");
  } catch (error) {
    console.error("Error upserting user:", error);
    throw error;
  }
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  app.use(getSession());
  app.use(passport.initialize());
  app.use(passport.session());

  const config = await getOidcConfig();

  const verify: VerifyFunction = async (
    tokens: client.TokenEndpointResponse & client.TokenEndpointResponseHelpers,
    verified: passport.AuthenticateCallback
  ) => {
    const user = {};
    updateUserSession(user, tokens);
    await upsertUser(tokens.claims());
    verified(null, user);
  };

  for (const domain of process.env
    .REPLIT_DOMAINS!.split(",")) {
    const strategy = new Strategy(
      {
        name: `replitauth:${domain}`,
        config,
        scope: "openid email profile offline_access",
        callbackURL: `https://${domain}/api/callback`,
      },
      verify,
    );
    passport.use(strategy);
  }

  passport.serializeUser((user: Express.User, cb) => cb(null, user));
  passport.deserializeUser((user: Express.User, cb) => cb(null, user));

  // For development environment, create a simpler login endpoint
  if (process.env.NODE_ENV === 'development') {
    app.get("/api/login", (req, res) => {
      // In development, create a mock user session for testing
      const mockUser = {
        id: "*********",
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
        profileImageUrl: null,
      };
      
      // Store in session
      req.login({
        claims: {
          sub: mockUser.id,
          email: mockUser.email,
          first_name: mockUser.firstName,
          last_name: mockUser.lastName,
          profile_image_url: mockUser.profileImageUrl,
          exp: Math.floor(Date.now() / 1000) + 3600,
        },
        access_token: "mock_access_token",
        refresh_token: "mock_refresh_token",
        expires_at: Math.floor(Date.now() / 1000) + 3600,
      }, async () => {
        try {
          // Upsert the user in the database
          await storage.upsertUser(mockUser);
          res.redirect("/");
        } catch (error) {
          console.error("Error upserting mock user:", error);
          res.status(500).send("Error creating mock user session");
        }
      });
    });
  } else {
    // Production login with Replit Auth
    app.get("/api/login", (req, res, next) => {
      passport.authenticate(`replitauth:${req.hostname}`, {
        prompt: "login consent",
        scope: ["openid", "email", "profile", "offline_access"],
      })(req, res, next);
    });
  }

  app.get("/api/callback", (req, res, next) => {
    passport.authenticate(`replitauth:${req.hostname}`, {
      successReturnToOrRedirect: "/",
      failureRedirect: "/api/login",
    })(req, res, next);
  });

  app.get("/api/logout", (req, res) => {
    if (process.env.NODE_ENV === 'development') {
      // In development, simply log out and redirect to login page
      req.logout(() => {
        res.redirect('/login');
      });
    } else {
      // In production, use OpenID Connect end session endpoint
      req.logout(() => {
        res.redirect(
          client.buildEndSessionUrl(config, {
            client_id: process.env.REPL_ID!,
            post_logout_redirect_uri: `${req.protocol}://${req.hostname}/login`,
          }).href
        );
      });
    }
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  const user = req.user as any;

  if (!req.isAuthenticated() || !user.expires_at) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  const now = Math.floor(Date.now() / 1000);
  if (now <= user.expires_at) {
    return next();
  }

  const refreshToken = user.refresh_token;
  if (!refreshToken) {
    return res.redirect("/api/login");
  }

  try {
    const config = await getOidcConfig();
    const tokenResponse = await client.refreshTokenGrant(config, refreshToken);
    updateUserSession(user, tokenResponse);
    return next();
  } catch (error) {
    return res.redirect("/api/login");
  }
};